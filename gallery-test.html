<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Modal Test - Nirmanah</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Gallery Modal Test</h1>
        <p class="text-center mb-8 text-gray-600">Click on any image to test the gallery modal with different aspect ratios</p>
        
        <!-- Test Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Test Image 1: Landscape -->
            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                 onclick="openGallery(0)">
                <div class="aspect-video overflow-hidden">
                    <img src="NIRMANAH PROJECTS/revised view1.jpg"
                         alt="Test landscape image"
                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
                </div>
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                    <h3 class="text-white font-semibold">Landscape Image</h3>
                    <p class="text-white/80 text-sm">Wide aspect ratio test</p>
                </div>
            </div>

            <!-- Test Image 2: Portrait -->
            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                 onclick="openGallery(1)">
                <div class="aspect-video overflow-hidden">
                    <img src="NIRMANAH PROJECTS/PHOTO-2023-05-16-21-47-54.jpg"
                         alt="Test portrait image"
                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
                </div>
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                    <h3 class="text-white font-semibold">Portrait Image</h3>
                    <p class="text-white/80 text-sm">Tall aspect ratio test</p>
                </div>
            </div>

            <!-- Test Image 3: Square -->
            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                 onclick="openGallery(2)">
                <div class="aspect-video overflow-hidden">
                    <img src="NIRMANAH PROJECTS/PHOTO-2023-06-06-13-52-28.jpg"
                         alt="Test square image"
                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
                </div>
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                    <h3 class="text-white font-semibold">Square Image</h3>
                    <p class="text-white/80 text-sm">1:1 aspect ratio test</p>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-12 bg-white rounded-lg p-6 shadow-lg">
            <h2 class="text-xl font-semibold mb-4">Test Instructions</h2>
            <ul class="space-y-2 text-gray-700">
                <li>• Click on any image to open the gallery modal</li>
                <li>• Test navigation between images using arrow buttons or keyboard arrows</li>
                <li>• Verify that the modal maintains consistent size regardless of image aspect ratio</li>
                <li>• Check that images are properly contained within their designated space</li>
                <li>• Test on different screen sizes (resize browser window)</li>
                <li>• Ensure smooth transitions and loading states work correctly</li>
            </ul>
        </div>
    </div>

    <!-- Gallery Modal (same as in index.html) -->
    <div id="galleryModal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-90 flex items-center justify-center p-4">
        <div class="gallery-modal-container relative bg-white rounded-2xl shadow-2xl overflow-hidden">
            <button onclick="closeGallery()" 
                    class="absolute top-4 right-4 z-10 w-10 h-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center text-white hover:text-gray-200 transition-all duration-200"
                    aria-label="Close gallery">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <div class="flex flex-col lg:flex-row h-full">
                <div class="gallery-image-section relative bg-gray-900">
                    <button onclick="prevImage()" 
                            class="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center text-white hover:text-gray-200 transition-all duration-200"
                            aria-label="Previous image">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    
                    <button onclick="nextImage()"
                            class="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center text-white hover:text-gray-200 transition-all duration-200"
                            aria-label="Next image">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    <div class="gallery-image-container">
                        <img id="modalImg" src="" alt="" class="gallery-main-image" />
                    </div>
                </div>

                <div class="gallery-details-section bg-white h-full">
                    <div class="flex flex-col h-full">
                        <div class="flex-shrink-0">
                            <h2 id="modalTitle" class="text-2xl font-bold mb-3 c-primary"></h2>

                            <div class="grid grid-cols-2 gap-3 mb-4">
                                <div>
                                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Category</h3>
                                    <p id="modalCategory" class="text-sm font-medium" style="color: #D4941E;"></p>
                                </div>
                                <div>
                                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Location</h3>
                                    <p id="modalLocation" class="text-sm c-secondary"></p>
                                </div>
                                <div>
                                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Year</h3>
                                    <p id="modalYear" class="text-sm c-secondary"></p>
                                </div>
                                <div>
                                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">Area</h3>
                                    <p id="modalArea" class="text-sm c-secondary"></p>
                                </div>
                            </div>
                        </div>

                        <div class="flex-1 flex flex-col min-h-0">
                            <div class="mb-4">
                                <h3 class="text-base font-semibold mb-2 c-primary">Project Overview</h3>
                                <p id="modalDesc" class="text-sm c-secondary leading-relaxed"></p>
                            </div>

                            <div class="mb-4">
                                <h3 class="text-base font-semibold mb-2 c-primary">Key Features</h3>
                                <ul id="modalFeatures" class="space-y-1 text-sm"></ul>
                            </div>

                            <div class="mb-4">
                                <h3 class="text-base font-semibold mb-2 c-primary">Services Provided</h3>
                                <p id="modalServices" class="text-sm c-secondary leading-relaxed"></p>
                            </div>
                        </div>

                        <!-- Contact CTA -->
                        <div class="flex-shrink-0 pt-4 border-t border-gray-200">
                            <div class="relative inline-flex items-center justify-center w-full group">
                                <div class="absolute transition-all duration-200 rounded-full -inset-px gradient-primary shadow-brand"></div>
                                <a href="#contact"
                                   onclick="closeGallery()"
                                   class="relative inline-flex items-center justify-center w-full px-4 py-2 text-sm font-normal border border-transparent rounded-full c-inverse gradient-primary hover:scale-105 transition-all duration-300"
                                   role="button">
                                    Start Your Project
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="main.js"></script>
</body>
</html>
