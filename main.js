// Navigation and dropdown interactions (moved from inline script)
(function() {
  function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.getElementById('menu-icon');
    const closeIcon = document.getElementById('close-icon');

    if (!mobileMenu || !menuIcon || !closeIcon) return;

    if (mobileMenu.classList.contains('hidden')) {
      mobileMenu.classList.remove('hidden');
      menuIcon.classList.add('hidden');
      closeIcon.classList.remove('hidden');
    } else {
      mobileMenu.classList.add('hidden');
      menuIcon.classList.remove('hidden');
      closeIcon.classList.add('hidden');
      // Close mobile dropdown when closing main menu
      const mobileDropdown = document.getElementById('mobile-dropdown-content');
      const mobileArrow = document.getElementById('mobile-dropdown-arrow');
      if (mobileDropdown) mobileDropdown.classList.remove('expanded');
      if (mobileArrow) mobileArrow.style.transform = 'rotate(0deg)';
    }
  }

  function toggleMobileDropdown() {
    const dropdown = document.getElementById('mobile-dropdown-content');
    const arrow = document.getElementById('mobile-dropdown-arrow');
    const button = arrow ? arrow.parentElement : null;

    if (!dropdown || !arrow || !button) return;

    if (dropdown.classList.contains('expanded')) {
      dropdown.classList.remove('expanded');
      arrow.style.transform = 'rotate(0deg)';
      button.setAttribute('aria-expanded', 'false');
    } else {
      dropdown.classList.add('expanded');
      arrow.style.transform = 'rotate(180deg)';
      button.setAttribute('aria-expanded', 'true');
    }
  }

  // expose handlers for HTML onclick attributes
  window.toggleMobileMenu = toggleMobileMenu;
  window.toggleMobileDropdown = toggleMobileDropdown;

  // Close mobile menu when window is resized to desktop
  window.addEventListener('resize', function() {
    if (window.innerWidth >= 768) {
      const mobileMenu = document.getElementById('mobile-menu');
      const menuIcon = document.getElementById('menu-icon');
      const closeIcon = document.getElementById('close-icon');
      if (mobileMenu) mobileMenu.classList.add('hidden');
      if (menuIcon) menuIcon.classList.remove('hidden');
      if (closeIcon) closeIcon.classList.add('hidden');

      // Reset mobile dropdown
      const mobileDropdown = document.getElementById('mobile-dropdown-content');
      const mobileArrow = document.getElementById('mobile-dropdown-arrow');
      if (mobileDropdown) mobileDropdown.classList.remove('expanded');
      if (mobileArrow) mobileArrow.style.transform = 'rotate(0deg)';
    }
  });

  // Keyboard navigation for dropdown
  document.addEventListener('keydown', function(e) {
    if (e.key !== 'Escape') return;
    const dropdown = document.querySelector('.dropdown');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    if (!dropdown || !dropdownMenu) return;
    dropdownMenu.classList.remove('show');
    const trigger = dropdown.querySelector('button, a');
    if (trigger) trigger.setAttribute('aria-expanded', 'false');
  });

  // Toggle desktop services dropdown on focus/blur and click
  const servicesTrigger = document.querySelector('.dropdown > button');
  const servicesMenu = document.getElementById('services-menu');

  if (servicesTrigger && servicesMenu) {
    servicesTrigger.addEventListener('click', function() {
      const isOpen = servicesMenu.classList.contains('show');
      servicesMenu.classList.toggle('show', !isOpen);
      servicesTrigger.setAttribute('aria-expanded', String(!isOpen));
    });

    servicesTrigger.addEventListener('focus', function() {
      servicesTrigger.setAttribute('aria-expanded', 'true');
      servicesMenu.classList.add('show');
    });

    servicesTrigger.addEventListener('blur', function() {
      setTimeout(() => {
        if (!servicesMenu.contains(document.activeElement)) {
          servicesTrigger.setAttribute('aria-expanded', 'false');
          servicesMenu.classList.remove('show');
        }
      }, 100);
    });

    servicesMenu.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('blur', function() {
        setTimeout(() => {
          if (!servicesMenu.contains(document.activeElement)) {
            servicesTrigger.setAttribute('aria-expanded', 'false');
            servicesMenu.classList.remove('show');
          }
        }, 100);
      });
    });
  }
})();

// Video Modal Functions
(function() {
  function playVideo() {
    const modal = document.getElementById('videoModal');
    const videoFrame = document.getElementById('videoFrame');

    if (!modal || !videoFrame) return;

    // Replace with actual video URL - for now using a placeholder
    // You can replace this with your actual YouTube, Vimeo, or direct video URL
    const videoUrl = 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&rel=0';

    videoFrame.src = videoUrl;
    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  }

  function closeVideo() {
    const modal = document.getElementById('videoModal');
    const videoFrame = document.getElementById('videoFrame');

    if (!modal || !videoFrame) return;

    modal.classList.add('hidden');
    modal.classList.remove('flex');
    videoFrame.src = ''; // Stop video playback

    // Restore body scroll
    document.body.style.overflow = '';
  }

  // Close modal when clicking outside the video
  document.addEventListener('click', function(e) {
    const modal = document.getElementById('videoModal');
    if (e.target === modal) {
      closeVideo();
    }
  });

  // Close modal with Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeVideo();
    }
  });

  // Expose functions globally
  window.playVideo = playVideo;
  window.closeVideo = closeVideo;
})();

/* ─────── 1. DATA  ─────────────────────────────────────────────────── */
const gallery = [
  {
    src: "NIRMANAH PROJECTS/revised view1.jpg",
    title: "Modern Residential Design",
    category: "Design & Consultancy",
    location: "Kochi, Kerala",
    year: "2024",
    area: "3 200 sq ft",
    desc: "Comprehensive architectural and interior design solution balancing aesthetics with functionality.",
    features: [
      "Open-plan living & dining",
      "Double-height atrium",
      "Passive cooling strategies"
    ],
    services: "Concept design, structural detailing, interior fit-out supervision"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2023-05-16-21-47-54.jpg",
    title: "Contemporary Home Renovation",
    category: "Renovation & Remodeling",
    location: "Kottayam, Kerala",
    year: "2023",
    area: "2 500 sq ft",
    desc: "Complete home transformation enhancing style, comfort, and usability for modern living.",
    features: [
      "New skylight above stairwell",
      "Modular kitchen upgrade",
      "Energy-efficient lighting overhaul"
    ],
    services: "Structural retrofit, interior redesign, project management"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2023-06-06-13-52-28.jpg",
    title: "Outdoor Living Space",
    category: "Landscaping & Outdoor Works",
    location: "Calicut, Kerala",
    year: "2023",
    area: "1 800 sq ft",
    desc: "Custom outdoor design featuring landscaping elements that blend beauty with function.",
    features: [
      "Pergola-covered sit-out",
      "Native plant palette",
      "Water feature with LED lighting"
    ],
    services: "Landscape architecture, hardscaping, lighting design"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2025-04-08-11-46-53.jpg",
    title: "Complete Project Solution",
    category: "Turnkey Projects",
    location: "Ernakulam, Kerala",
    year: "2025",
    area: "4 800 sq ft",
    desc: "End-to-end design-to-delivery solution including construction and project management.",
    features: [
      "Integrated BIM workflow",
      "Solar rooftop installation",
      "Smart-home automation"
    ],
    services: "Architecture, interior, MEP coordination, construction"
  },
  {
    src: "NIRMANAH PROJECTS/PHOTO-2023-05-16-22-02-55.jpg",
    title: "Modern Interior Design",
    category: "Design & Consultancy",
    location: "Thrissur, Kerala",
    year: "2023",
    area: "1 400 sq ft",
    desc: "Sophisticated interior design combining contemporary aesthetics with practical functionality.",
    features: [
      "Custom furniture design",
      "Hidden storage solutions",
      "Warm indirect lighting"
    ],
    services: "Interior design, FF&E procurement, site supervision"
  },
  {
    src: "NIRMANAH PROJECTS/v1 (1).jpg",
    title: "Architectural Excellence",
    category: "Design & Consultancy",
    location: "Alappuzha, Kerala",
    year: "2024",
    area: "5 600 sq ft",
    desc: "Innovative architectural design showcasing structural integrity and aesthetic appeal.",
    features: [
      "Cantilevered balconies",
      "Rainwater harvesting",
      "Facade shading fins"
    ],
    services: "Concept-to-detailed design, structural consultancy"
  }
];

/* ─────── 2. STATE  ────────────────────────────────────────────────── */
let currentIndex = 0;

/* ─────── 3. OPEN / CLOSE  ─────────────────────────────────────────── */
function openGallery(index) {
  currentIndex = index;
  fillModal();
  document.getElementById("galleryModal").classList.remove("hidden");
  document.body.style.overflow = "hidden";
}

function closeGallery() {
  document.getElementById("galleryModal").classList.add("hidden");
  document.body.style.overflow = "auto";
}

/* ─────── 4. NAVIGATION  ───────────────────────────────────────────── */
function prevImage() {
  currentIndex = (currentIndex - 1 + gallery.length) % gallery.length;
  fillModal();
}

function nextImage() {
  currentIndex = (currentIndex + 1) % gallery.length;
  fillModal();
}

/* ─────── 5. POPULATE MODAL  ───────────────────────────────────────── */
function fillModal() {
  const item = gallery[currentIndex];
  const modalImg = document.getElementById("modalImg");
  const imageContainer = document.querySelector(".gallery-image-container");

  // Add loading state
  if (imageContainer) {
    imageContainer.classList.add("loading");
  }
  if (modalImg) {
    modalImg.classList.add("loading");
  }

  // Create new image to preload
  const newImg = new Image();
  newImg.onload = function() {
    // Remove loading state and update image
    if (imageContainer) {
      imageContainer.classList.remove("loading");
    }
    if (modalImg) {
      modalImg.src = item.src;
      modalImg.alt = item.title;
      modalImg.classList.remove("loading");
    }
  };

  newImg.onerror = function() {
    // Handle error - remove loading state and show placeholder or error message
    if (imageContainer) {
      imageContainer.classList.remove("loading");
    }
    if (modalImg) {
      modalImg.classList.remove("loading");
      // You could set a placeholder image here if needed
      modalImg.src = item.src; // Still try to load the original
      modalImg.alt = item.title;
    }
  };

  // Start loading the image
  newImg.src = item.src;

  // Update text content immediately
  const modalTitle = document.getElementById("modalTitle");
  const modalCategory = document.getElementById("modalCategory");
  const modalLocation = document.getElementById("modalLocation");
  const modalYear = document.getElementById("modalYear");
  const modalArea = document.getElementById("modalArea");
  const modalDesc = document.getElementById("modalDesc");
  const modalServices = document.getElementById("modalServices");

  if (modalTitle) modalTitle.textContent = item.title;
  if (modalCategory) modalCategory.textContent = item.category;
  if (modalLocation) modalLocation.textContent = item.location;
  if (modalYear) modalYear.textContent = item.year;
  if (modalArea) modalArea.textContent = item.area;
  if (modalDesc) modalDesc.textContent = item.desc;
  if (modalServices) modalServices.textContent = item.services;

  /* Features list */
  const ul = document.getElementById("modalFeatures");
  if (ul) {
    ul.innerHTML = "";
    item.features.forEach(f => {
      const li = document.createElement("li");
      li.textContent = f;
      ul.appendChild(li);
    });
  }
}

/* ─────── 6. EVENT HELPERS  ───────────────────────────────────────── */
document.getElementById("galleryModal").addEventListener("click", e => {
  if (e.target === e.currentTarget) closeGallery();      // click outside content
});

document.addEventListener("keydown", e => {
  if (e.key === "Escape") closeGallery();
  if (e.key === "ArrowLeft") prevImage();
  if (e.key === "ArrowRight") nextImage();
});

/* ─────── 7. EXPOSE FUNCTIONS GLOBALLY  ──────────────────────────────── */
window.openGallery = openGallery;
window.closeGallery = closeGallery;
window.prevImage = prevImage;
window.nextImage = nextImage;

