<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="<PERSON><PERSON><PERSON> is a design & build studio creating stunning spaces from concept to completion. Interior design, renovation, landscaping, and turnkey projects." />
  <meta name="robots" content="index, follow" />
  <meta name="theme-color" content="#D4941E" />
  <!-- Canonical: update href to your production URL -->
  <!-- <link rel="canonical" href="https://www.your-domain.com/" /> -->

  <!-- Preconnects for faster font loading -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://use.typekit.net" crossorigin>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://use.typekit.net/oph7lmk.css"> <!-- Acumin Variable Concept via Adobe Fonts -->

  <!-- Styles -->
  <link rel="stylesheet" href="style.css">

  <!-- Tailwind (CDN for dev/preview; consider a production build to purge unused styles) -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Favicon -->
  <link rel="icon" href="logo.png" type="image/png">

  <!-- Open Graph / Twitter Cards -->
  <meta property="og:type" content="website" />
  <meta property="og:title" content="Nirmanah Design & Build" />
  <meta property="og:description" content="Transform your space with Nirmanah — design & build excellence for homes and businesses." />
  <meta property="og:image" content="iqbal-putra-gBTb_dnfJGw-unsplash.jpg" />
  <meta property="og:image:alt" content="Modern architectural design showcasing Nirmanah's expertise" />
  <meta property="og:site_name" content="Nirmanah" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Nirmanah Design & Build" />
  <meta name="twitter:description" content="From concept to completion, we create stunning spaces that reflect your vision." />
  <meta name="twitter:image" content="iqbal-putra-gBTb_dnfJGw-unsplash.jpg" />

  <title>Nirmanah Design & Build</title>
</head>
<body class="bg-gray-50 min-h-screen">
  <a class="skip-link" href="#main-content">Skip to main content</a>
  <!-- Navbar -->
  <header>
  <nav class="flex items-center justify-between px-8 py-4 bg-white  sticky top-0 z-50" role="navigation" aria-label="Primary">
    <!-- Logo and Tagline -->
    <div class="flex items-center space-x-2">
      <img src="logo.png" alt="Nirmanah logo" class="w-10 h-10 object-contain" width="40" height="40">
      <div class="flex flex-col">
        <span class="text-xl text-gray-900 nirmanah-font">nirmanah</span>
        <span class="tagline">design & build</span>
      </div>
    </div>

    <!-- Nav Links Container -->
    <ul class="hidden md:flex items-center space-x-1 nav-links bg-gray-900 rounded-full p-2 shadow-md">
      <li>
        <a href="#" aria-current="page" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Home</a>
      </li>
      <li>
        <a href="#about" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">About</a>
      </li>
      <li class="dropdown">
        <button type="button" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm flex items-center text-gray-300"
           aria-haspopup="true" aria-expanded="false" aria-controls="services-menu">
          <span>Services</span>
          <svg class="dropdown-arrow w-3 h-3 fill-current" viewBox="0 0 12 12" aria-hidden="true">
            <path d="M6 8l4-4H2z"/>
          </svg>
        </button>
        <div id="services-menu" class="dropdown-menu" role="menu">
          <a href="#" class="dropdown-item" role="menuitem">Design & Consultancy</a>
          <a href="#" class="dropdown-item" role="menuitem">Renovation & Remodeling</a>
          <a href="#" class="dropdown-item" role="menuitem">Landscaping & Outdoor Work</a>
          <a href="#" class="dropdown-item" role="menuitem">Turnkey Projects</a>
        </div>
      </li>
      <li>
        <a href="#portfolio" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Portfolio</a>
      </li>
      <li>
        <a href="#contact" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Contact</a>
      </li>
    </ul>

    <!-- Mobile Menu Button -->
    <div class="md:hidden">
      <button onclick="toggleMobileMenu()" class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200" aria-label="Toggle mobile menu">
        <svg id="menu-icon" class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
        <svg id="close-icon" class="w-6 h-6 text-gray-700 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Get a Quote Button -->
    <div class="hidden md:block">
      <button class="btn-primary">
        Get a Quote
      </button>
    </div>
  </nav>
  </header>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden bg-white border-b shadow-lg hidden">
    <div class="px-8 py-4 space-y-4">
      <a href="#" class="block py-2 text-gray-900 font-medium border-l-4 border-orange-500 pl-4 bg-gray-50 mobile-menu-link active">
        Home
      </a>
      <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">About</a>

      <!-- Mobile Services Dropdown -->
      <div class="mobile-dropdown">
        <button onclick="toggleMobileDropdown()" class="flex items-center justify-between w-full py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link" aria-expanded="false">
          Services
          <svg id="mobile-dropdown-arrow" class="w-4 h-4 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
          </svg>
        </button>
        <div id="mobile-dropdown-content" class="mobile-dropdown-content">
          <a href="#" class="block mobile-dropdown-item px-2 py-2">Design & Consultancy</a>
          <a href="#" class="block mobile-dropdown-item px-2 py-2">Renovation & Remodeling</a>
          <a href="#" class="block mobile-dropdown-item px-2 py-2">Landscaping & Outdoor Work</a>
          <a href="#" class="block mobile-dropdown-item px-2 py-2">Turnkey Projects</a>
        </div>
      </div>

      <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">Portfolio</a>
      <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">Contact</a>
      <hr class="my-4">
      <div class="space-y-3">
        <button class="block w-full py-3 btn-primary rounded-full font-medium">
          Get a Quote
        </button>
      </div>
    </div>
  </div>

  <main id="main-content">
  <!-- Hero Section -->
  <section class="relative pt-12 overflow-hidden bg-white sm:pt-16">
      <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
          <div class="max-w-4xl mx-auto text-center">
              <p class="text-sm font-normal tracking-widest uppercase">
          <span class="text-transparent bg-clip-text text-gradient-brand"> Design & Build Excellence </span>
              </p>
        <h1 class="mt-8 text-4xl font-normal sm:text-5xl lg:text-6xl xl:text-7xl c-primary">Transform Your Space with Nirmanah</h1>
        <p class="mt-6 text-lg sm:text-xl c-secondary">From concept to completion, we create stunning spaces that reflect your vision and enhance your lifestyle.</p>

              <div class="flex flex-col items-center justify-center px-8 mt-12 space-y-5 sm:space-y-0 sm:px-0 sm:space-x-5 sm:flex-row">
                  <div class="relative inline-flex items-center justify-center w-full sm:w-auto group">
            <div class="absolute transition-all duration-200 rounded-full -inset-px gradient-primary shadow-brand"></div>
            <a href="#contact" title="" class="relative inline-flex items-center justify-center w-full px-8 py-3 text-base font-normal border border-transparent rounded-full sm:w-auto c-inverse gradient-primary" role="button"> Get Your Quote </a>
                  </div>

          <a href="#portfolio" title="" class="inline-flex items-center justify-center w-full px-8 py-3 text-base font-normal transition-all duration-200 rounded-full sm:w-auto btn-outline-brand" role="button"> View Portfolio </a>
              </div>
          </div>

          <!-- <div class="relative mt-12 -mb-4 sm:-mb-10 lg:-mb-12 sm:mt-16 lg:mt-24">
              <div class="absolute top-0 transform -translate-x-1/2 left-1/2">
                  <svg class="blur-3xl filter" style="filter: blur(64px)" width="645" height="413" viewBox="0 0 645 413" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M181.316 218.778C86.2529 123.715 -63.7045 134.94 31.3589 39.8762C126.422 -55.1873 528.427 41.1918 623.49 136.255C718.554 231.319 470.678 289.068 375.614 384.131C280.551 479.195 276.38 313.842 181.316 218.778Z" fill="url(#nirmanah-gradient)" />
                      <defs>
                          <linearGradient id="nirmanah-gradient" x1="665.741" y1="178.506" x2="296.286" y2="474.62" gradientUnits="userSpaceOnUse">
                              <stop offset="0%" style="stop-color: var(--color-primary)" />
                              <stop offset="100%" style="stop-color: var(--color-secondary-500)" />
                          </linearGradient>
                      </defs>
                  </svg>
              </div>

               <div class="absolute inset-0">
                  <img class="object-cover w-full h-full opacity-30" src="https://landingfoliocom.imgix.net/store/collection/dusk/images/noise.png" alt="" />
              </div> 

              <div class="relative">
                  <img class="relative w-full max-w-5xl mx-auto rounded-lg shadow-2xl" src="iqbal-putra-gBTb_dnfJGw-unsplash.jpg" alt="Modern architectural design showcasing Nirmanah's design and build expertise" />
                  <div class="absolute inset-0 rounded-lg" style="background: linear-gradient(to top, rgba(23, 23, 23, 0.3), transparent);"></div>
              </div> 
          </div> -->
      </div>
  </section>
  </main>

  <!-- Structured Data: Organization (update URL and social profiles as applicable) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Nirmanah",
    "url": "https://www.your-domain.com/",
    "logo": "https://www.your-domain.com/Logonirmanah.png"
  }
  </script>

  <script src="main.js" defer></script>
</body>
</html>
