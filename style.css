/* Nirmanah Design & Build - Essential Variables */
:root {
  /* Primary Brand Colors - Used */
  --color-primary: #D4941E;           /* Main bronze-orange */
  --color-primary-50: #FEF7E8;        /* Used in dropdown hover */
  --color-primary-600: #B8751A;       /* Used in button hover */

  /* Secondary Colors - Used */
  --color-secondary-500: #8B5A2B;     /* Used in tagline */

  /* Neutral Colors - Used */
  --color-neutral-white: #FFFFFF;     /* Used in nav-item-active */
  --color-neutral-300: #D4D4D4;       /* Used in nav-item-hover */
  --color-neutral-900: #171717;       /* Used in nav-links */

  /* Text Colors - Used */
  --text-primary: #171717;            /* Primary text - dark */
  --text-secondary: #525252;          /* Secondary text - medium gray */
  --text-muted: #737373;              /* Muted text - light gray */
  --text-inverse: #FFFFFF;            /* Text on dark backgrounds */

  /* Background Colors - Used */
  --bg-primary: #FFFFFF;              /* Main background */
  --bg-secondary: #F9F9F9;            /* Secondary background */
  --bg-tertiary: #F3F3F3;             /* Tertiary background */
  --bg-brand-light: #FEF7E8;          /* Light brand background */

  /* Border Colors - Used */
  --border-light: #E5E5E5;            /* Light borders */

  /* Shadow Colors - Used */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-brand: 0 4px 14px 0 rgba(212, 148, 30, 0.25);
}

/* Typography */
body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
  /* background-color: var(--bg-secondary); */
  margin: 0;
  padding: 0;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
  margin: 0 0 1rem 0;
}

.nirmanah-font {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
}

.tagline {
  font-family: 'acumin-variable-concept', sans-serif;
  font-weight: 700;
  font-size: 9px;
  text-align: center;
  letter-spacing: 2.0px;
  text-transform: uppercase;
  color: var(--color-secondary-500);
}

/* Buttons */
.btn-primary {
  background-color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary-500));
  color: var(--text-inverse);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-brand);
}

/* Outline Brand Button */
.btn-outline-brand {
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}
.btn-outline-brand:hover {
  border-color: var(--color-primary-600);
  color: var(--color-primary-600);
}

/* Nav Links */
.nav-links {
  background-color: var(--color-neutral-900);
  border-radius: 9999px;
  padding: 0.5rem;
  box-shadow: var(--shadow-md);
}

.nav-item-hover {
  color: var(--color-neutral-300);
  border-radius: 9999px;
  transition: all 0.2s ease;
}

.nav-item-hover:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--color-primary);
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--bg-primary);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-lg);
  min-width: 240px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 0.5rem;
  border: 1px solid var(--border-light);
}

.dropdown:hover .dropdown-menu,
.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-light);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: var(--color-primary-50);
  color: var(--color-primary);
  padding-left: 1.25rem;
}

.dropdown-item:first-child {
  border-radius: 0.75rem 0.75rem 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 0.75rem 0.75rem;
}

/* Dropdown Arrow */
.dropdown-arrow {
  transition: transform 0.3s ease;
  margin-left: 0.25rem;
  display: inline-block;
}

.dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* Mobile Menu */
#mobile-menu {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
}

.mobile-dropdown {
  position: relative;
}

.mobile-menu-link {
  display: block;
  padding: 0.75rem 0;
  color: var(--text-secondary);
  transition: color 0.2s ease;
  text-align: left;
  background: none;
  border: none;
  width: 100%;
  font-size: inherit;
  font-family: inherit;
}

.mobile-menu-link:hover, 
.mobile-menu-link:focus {
  color: var(--color-primary);
}

.mobile-menu-link.active {
  color: var(--text-primary);
  border-left: 4px solid var(--color-primary);
  padding-left: 1rem;
  background-color: var(--bg-tertiary);
}

/* Mobile Dropdown Styles */
.mobile-dropdown-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  margin-left: 0;
  padding-left: 0;
}

.mobile-dropdown-content.expanded {
  max-height: 300px;
  padding-top: 0.5rem;
}

.mobile-dropdown-item {
  display: block;
  padding: 0.5rem 0 0.5rem 1rem;
  margin-left: 1rem;
  color: var(--text-muted);
  font-size: 0.875rem;
  border-left: 2px solid transparent;
  transition: all 0.2s ease;
  text-decoration: none;
}

.mobile-dropdown-item:hover {
  color: var(--color-primary);
  border-left-color: var(--color-primary);
  background-color: var(--color-primary-50);
  padding-left: 1.25rem;
}

/* Accessibility helpers */
.skip-link {
  position: absolute;
  left: -999px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.skip-link:focus {
  left: 1rem;
  top: 1rem;
  width: auto;
  height: auto;
  background: #fff;
  color: #000;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-md);
  z-index: 10000;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Utilities */
.c-primary { color: var(--text-primary); }
.c-secondary { color: var(--text-secondary); }
.c-inverse { color: var(--text-inverse); }
.gradient-primary { background: linear-gradient(135deg, var(--color-primary), var(--color-secondary-500)); }
.shadow-brand { box-shadow: var(--shadow-brand); }
.text-gradient-brand {
  background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary-500));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
/* From Uiverse.io by andrew-demchenk0 */ 
.card {
  --main-color: #323232;
  --sub-color: #5b5b5b;
  --bg-color: #c7c7c7;
  --accent-color: #242C88;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  width: 270px;
  padding: 25px;
  border-radius: 25px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  background-color: var(--bg-color);
  transition: all 0.2s;
  cursor: pointer;
  box-shadow: 0px 0px 5px -1.5px #000;
}

.card::before {
  content: '';
  width: 99%;
  height: 99%;
  background: var(--sub-color);
  position: absolute;
  z-index: -1;
  top: 1px;
  left: 1px;
  border-radius: 25px;
  transition: all 0.3s;
}

.card__wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.card__img {
  width: 100%;
}

.card__title {
  color: var(--main-color);
  font-weight: 900;
  font-size: 25px;
}

.card__subtitle {
  color: var(--sub-color);
  font-weight: 600;
  font-size: 20px;
}

.card__icon {
  width: 40px;
  height: 40px;
  transform: rotate(-45deg);
  transition: all 0.3s;
}

.card__icon svg {
  width: 100%;
  height: 100%;
}

.card__icon g {
  fill: var(--main-color);
}

.card__icon circle, polyline, line {
  stroke: var(--main-color);
}

.card:hover .card__icon {
  transform: rotate(0);
}

.card:hover .card__icon circle, .card:hover .card__icon polyline, .card:hover .card__icon line {
  stroke: var(--accent-color);
}

.card:hover .card__icon g {
  fill: var(--accent-color);
}

.card:hover {
  box-shadow: 0px 0px 20px -5px #000;
}

.card:hover::before {
  transform: rotate(10deg);
  box-shadow: 0px 0px 20px -5px #000;
}

/* remove svg from class ="card__img" and last styles below css code before use */
.st1 {
  fill: #fff
}

.st2 {
  fill: none;
  stroke: #242c88;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-miterlimit: 10
}

.st7 {
  fill: #589fff
}

.st9 {
  fill: #bc8d66
}

.st15,.st16 {
  opacity: .4;
  fill: #242c88
}

.st16 {
  opacity: .2
}

.st18 {
  fill: #ffc408
}

.st53 {
  fill: none;
  stroke: #242c88;
  stroke-width: 1.848;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-miterlimit: 10
}

/* Gallery Modal Styles */
#galleryModal {
  backdrop-filter: blur(4px);
}

#galleryModal .bg-black {
  background-color: rgba(0, 0, 0, 0.95);
}

/* Fixed Size Modal Container */
.gallery-modal-container {
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  max-height: 800px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

/* Desktop Layout */
@media (min-width: 1024px) {
  .gallery-modal-container {
    flex-direction: row;
  }

  .gallery-image-section {
    width: 66.666667%; /* 2/3 */
    height: 100%;
  }

  .gallery-details-section {
    width: 33.333333%; /* 1/3 */
    height: 100%;
    padding: 2rem;
  }
}

/* Image Section Styling */
.gallery-image-section {
  position: relative;
  background-color: #111827; /* gray-900 */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Fixed Image Container */
.gallery-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Main Image Styling */
.gallery-main-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  transition: opacity 0.3s ease;
  border-radius: 0;
}

/* Details Section */
.gallery-details-section {
  padding: 1.5rem;
  background-color: white;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) transparent;
}

/* Custom Scrollbar for Webkit browsers */
.gallery-details-section::-webkit-scrollbar {
  width: 6px;
}

.gallery-details-section::-webkit-scrollbar-track {
  background: transparent;
}

.gallery-details-section::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 3px;
}

.gallery-details-section::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-primary-600);
}

/* Gallery Modal Features List */
#modalFeatures li {
  position: relative;
  padding-left: 1.5rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

#modalFeatures li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: bold;
  font-size: 1.2em;
}

/* Mobile Responsive Adjustments */
@media (max-width: 1023px) {
  .gallery-modal-container {
    width: 95vw;
    height: 90vh;
    flex-direction: column;
  }

  .gallery-image-section {
    width: 100%;
    height: 60%;
    min-height: 300px;
  }

  .gallery-details-section {
    width: 100%;
    height: 40%;
    padding: 1rem;
  }
}

/* Small Mobile Adjustments */
@media (max-width: 640px) {
  .gallery-modal-container {
    width: 98vw;
    height: 95vh;
    min-height: 500px;
  }

  .gallery-image-section {
    height: 50%;
  }

  .gallery-details-section {
    height: 50%;
    padding: 0.75rem;
  }
}

/* Gallery Modal Animation */
#galleryModal.hidden {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

#galleryModal:not(.hidden) {
  opacity: 1;
  pointer-events: auto;
  transition: opacity 0.3s ease;
}

/* Image Loading States */
.gallery-main-image {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.gallery-main-image.loading {
  opacity: 0.7;
}

/* Navigation Button Enhancements */
.gallery-image-section button {
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
}

.gallery-image-section button:hover {
  transform: scale(1.05);
  backdrop-filter: blur(12px);
}

/* Prevent image selection and dragging */
.gallery-main-image {
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}

/* Loading placeholder for images */
.gallery-image-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-primary);
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-image-container.loading::before {
  opacity: 1;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* High DPI Display Optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .gallery-main-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}